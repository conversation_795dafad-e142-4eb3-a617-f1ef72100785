// ==UserScript==
// @name         LootLabs Captcha Solver Pro
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  Advanced captcha solver for LootLabs with modern GUI and real-time functionality
// <AUTHOR>
// @match        https://*.lootlabs.com/*
// @match        https://lootlabs.com/*
// @match        https://*.getswift.gg/*
// @match        https://getswift.gg/*
// @match        https://key.getswift.gg/*
// @match        https://*.hcaptcha.com/*captcha*
// @match        https://*.recaptcha.net/*
// @match        https://*.google.com/recaptcha/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_notification
// @connect      2captcha.com
// @connect      api.2captcha.com
// @run-at       document-end
// @updateURL    https://raw.githubusercontent.com/your-repo/lootlabs-captcha-solver/main/lootlabs-captcha-solver.user.js
// @downloadURL  https://raw.githubusercontent.com/your-repo/lootlabs-captcha-solver/main/lootlabs-captcha-solver.user.js
// ==/UserScript==

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        DEBUG: true,
        DETECTION_INTERVAL: 500, // Faster detection for instant solving
        MAX_SOLVE_ATTEMPTS: 3,
        OCR_CONFIDENCE_THRESHOLD: GM_getValue('ocr_confidence', 0.7),
        PATTERN_MATCH_THRESHOLD: GM_getValue('pattern_match', 0.8),
        AUTO_SUBMIT: GM_getValue('auto_submit', true),
        SUBMIT_DELAY: 100, // Faster submission for GetSwift
        GETSWIFT_INSTANT_MODE: true // Enable instant solving for GetSwift
    };

    // Modern CSS Styles
    const STYLES = `
        .ema-captcha-solver {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 320px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            z-index: 2147483647;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            cursor: move;
            user-select: none;
            pointer-events: auto;
        }

        .ema-header {
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            cursor: grab;
            user-select: none;
            position: relative;
            z-index: 2147483647;
        }

        .ema-header:active {
            cursor: grabbing;
        }

        .ema-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ema-status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4ade80;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .ema-body {
            padding: 20px;
        }

        .ema-status {
            font-size: 14px;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            border-left: 4px solid #4ade80;
        }

        .ema-progress {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .ema-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4ade80, #22d3ee);
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .ema-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .ema-stat {
            text-align: center;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }

        .ema-stat-value {
            font-size: 20px;
            font-weight: 600;
            display: block;
        }

        .ema-stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .ema-controls {
            display: flex;
            gap: 10px;
        }

        .ema-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            pointer-events: auto;
            position: relative;
            z-index: 2147483647;
        }

        .ema-btn-primary {
            background: #4ade80;
            color: white;
        }

        .ema-btn-primary:hover {
            background: #22c55e;
            transform: translateY(-2px);
        }

        .ema-btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .ema-btn-secondary:hover {
            background: rgba(255,255,255,0.3);
        }

        .ema-settings {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .ema-input {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 14px;
            margin-bottom: 10px;
            pointer-events: auto;
            position: relative;
            z-index: 2147483647;
            box-sizing: border-box;
        }

        .ema-input::placeholder {
            color: rgba(255,255,255,0.6);
        }

        .ema-log {
            max-height: 150px;
            overflow-y: auto;
            background: rgba(0,0,0,0.2);
            border-radius: 8px;
            padding: 10px;
            font-size: 12px;
            font-family: 'Courier New', monospace;
            margin-top: 10px;
        }

        .ema-log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .ema-log-success { color: #4ade80; }
        .ema-log-error { color: #ef4444; }
        .ema-log-info { color: #60a5fa; }
        .ema-log-warning { color: #f59e0b; }

        .ema-minimized {
            width: 60px;
            height: 60px;
        }

        .ema-minimized .ema-body,
        .ema-minimized .ema-header {
            display: none;
        }

        .ema-toggle {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            opacity: 0.7;
            z-index: 2147483647;
            pointer-events: auto;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .ema-toggle:hover {
            opacity: 1;
            background: rgba(255,255,255,0.1);
        }

        .ema-setting-group {
            margin-bottom: 15px;
        }

        .ema-label {
            display: block;
            font-size: 12px;
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .ema-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: rgba(255,255,255,0.2);
            outline: none;
            -webkit-appearance: none;
        }

        .ema-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #4ade80;
            cursor: pointer;
        }

        .ema-slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #4ade80;
            cursor: pointer;
            border: none;
        }

        .ema-checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
        }

        .ema-checkbox {
            width: 16px;
            height: 16px;
            accent-color: #4ade80;
        }
    `;

    // Captcha Solver Class
    class CaptchaSolver {
        constructor() {
            this.isActive = false;
            this.solvedCount = 0;
            this.failedCount = 0;
            this.currentTask = null;
            this.gui = null;
            this.logs = [];

            this.init();
        }

        init() {
            this.addStyles();
            this.createGUI();
            this.startMonitoring();
            this.log('Captcha Solver initialized', 'success');

            // Immediate GetSwift detection and solving
            if (window.location.hostname.includes('getswift.gg')) {
                this.log('GetSwift.gg detected - enabling instant mode', 'info');
                this.isActive = true; // Auto-start for GetSwift
                this.updateGUI();

                // Immediate attempt
                setTimeout(() => this.solveGetSwiftCaptcha(), 100);

                // Additional attempts with different timings
                setTimeout(() => this.solveGetSwiftCaptcha(), 500);
                setTimeout(() => this.solveGetSwiftCaptcha(), 1000);
                setTimeout(() => this.solveGetSwiftCaptcha(), 2000);
            }
        }

        addStyles() {
            GM_addStyle(STYLES);
        }

        createGUI() {
            this.gui = document.createElement('div');
            this.gui.className = 'ema-captcha-solver';
            this.gui.innerHTML = this.getGUIHTML();

            // Prevent click propagation to avoid triggering ads
            this.gui.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
            }, true);

            // Prevent all mouse events from bubbling
            this.gui.addEventListener('mousedown', (e) => e.stopPropagation(), true);
            this.gui.addEventListener('mouseup', (e) => e.stopPropagation(), true);
            this.gui.addEventListener('mousemove', (e) => e.stopPropagation(), true);

            document.body.appendChild(this.gui);

            this.makeDraggable();
            this.bindEvents();
            this.updateGUI();
        }

        getGUIHTML() {
            return `
                <button class="ema-toggle" id="ema-minimize-btn">−</button>
                <div class="ema-header">
                    <h3 class="ema-title">
                        <div class="ema-status-indicator"></div>
                        EMA Captcha Solver
                    </h3>
                </div>
                <div class="ema-body">
                    <div class="ema-status" id="ema-status">Ready to solve captchas</div>
                    <div class="ema-progress">
                        <div class="ema-progress-bar" id="ema-progress"></div>
                    </div>
                    <div class="ema-stats">
                        <div class="ema-stat">
                            <span class="ema-stat-value" id="ema-solved">0</span>
                            <span class="ema-stat-label">Solved</span>
                        </div>
                        <div class="ema-stat">
                            <span class="ema-stat-value" id="ema-failed">0</span>
                            <span class="ema-stat-label">Failed</span>
                        </div>
                    </div>
                    <div class="ema-controls">
                        <button class="ema-btn ema-btn-primary" id="ema-toggle-btn">Start</button>
                        <button class="ema-btn ema-btn-secondary" id="ema-settings-btn">Settings</button>
                    </div>
                    <div class="ema-settings" id="ema-settings" style="display: none;">
                        <div class="ema-setting-group">
                            <label class="ema-label">OCR Confidence: <span id="ocr-confidence-value">${CONFIG.OCR_CONFIDENCE_THRESHOLD}</span></label>
                            <input type="range" class="ema-slider" id="ocr-confidence" min="0.1" max="1" step="0.1" value="${CONFIG.OCR_CONFIDENCE_THRESHOLD}">
                        </div>
                        <div class="ema-setting-group">
                            <label class="ema-label">Pattern Match: <span id="pattern-match-value">${CONFIG.PATTERN_MATCH_THRESHOLD}</span></label>
                            <input type="range" class="ema-slider" id="pattern-match" min="0.1" max="1" step="0.1" value="${CONFIG.PATTERN_MATCH_THRESHOLD}">
                        </div>
                        <div class="ema-setting-group">
                            <label class="ema-checkbox-label">
                                <input type="checkbox" class="ema-checkbox" id="auto-submit" ${CONFIG.AUTO_SUBMIT ? 'checked' : ''}>
                                Auto Submit Forms
                            </label>
                        </div>
                        <button class="ema-btn ema-btn-primary" id="ema-save-settings">Save Settings</button>
                    </div>
                    <div class="ema-log" id="ema-log"></div>
                </div>
            `;
        }

        makeDraggable() {
            let isDragging = false;
            let currentX;
            let currentY;
            let initialX;
            let initialY;
            let xOffset = 0;
            let yOffset = 0;

            const header = this.gui.querySelector('.ema-header');

            header.addEventListener('mousedown', (e) => {
                e.stopPropagation();
                e.preventDefault();

                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;

                if (e.target === header || header.contains(e.target)) {
                    isDragging = true;
                    this.gui.style.cursor = 'grabbing';
                }
            });

            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    e.preventDefault();
                    e.stopPropagation();

                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;

                    xOffset = currentX;
                    yOffset = currentY;

                    // Keep within viewport bounds
                    const rect = this.gui.getBoundingClientRect();
                    const maxX = window.innerWidth - rect.width;
                    const maxY = window.innerHeight - rect.height;

                    currentX = Math.max(0, Math.min(currentX, maxX));
                    currentY = Math.max(0, Math.min(currentY, maxY));

                    this.gui.style.left = currentX + 'px';
                    this.gui.style.top = currentY + 'px';
                    this.gui.style.right = 'auto';
                    this.gui.style.bottom = 'auto';
                }
            });

            document.addEventListener('mouseup', (e) => {
                if (isDragging) {
                    e.stopPropagation();
                    e.preventDefault();
                }

                initialX = currentX;
                initialY = currentY;
                isDragging = false;
                this.gui.style.cursor = 'move';
            });

            // Make header cursor indicate draggable
            header.style.cursor = 'grab';
        }

        bindEvents() {
            const toggleBtn = this.gui.querySelector('#ema-toggle-btn');
            const settingsBtn = this.gui.querySelector('#ema-settings-btn');
            const saveSettingsBtn = this.gui.querySelector('#ema-save-settings');
            const minimizeBtn = this.gui.querySelector('#ema-minimize-btn');

            // Settings controls
            const ocrConfidenceSlider = this.gui.querySelector('#ocr-confidence');
            const patternMatchSlider = this.gui.querySelector('#pattern-match');
            const autoSubmitCheckbox = this.gui.querySelector('#auto-submit');

            // Add safe click handlers that prevent propagation
            toggleBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                this.toggleSolver();
            });

            settingsBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                this.toggleSettings();
            });

            saveSettingsBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                this.saveSettings();
            });

            minimizeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                this.gui.classList.toggle('ema-minimized');
            });

            // Settings sliders
            ocrConfidenceSlider.addEventListener('input', (e) => {
                e.stopPropagation();
                CONFIG.OCR_CONFIDENCE_THRESHOLD = parseFloat(e.target.value);
                this.gui.querySelector('#ocr-confidence-value').textContent = e.target.value;
                GM_setValue('ocr_confidence', e.target.value);
            });

            patternMatchSlider.addEventListener('input', (e) => {
                e.stopPropagation();
                CONFIG.PATTERN_MATCH_THRESHOLD = parseFloat(e.target.value);
                this.gui.querySelector('#pattern-match-value').textContent = e.target.value;
                GM_setValue('pattern_match', e.target.value);
            });

            autoSubmitCheckbox.addEventListener('change', (e) => {
                e.stopPropagation();
                CONFIG.AUTO_SUBMIT = e.target.checked;
                GM_setValue('auto_submit', e.target.checked);
            });
        }

        toggleSolver() {
            this.isActive = !this.isActive;
            const btn = this.gui.querySelector('#ema-toggle-btn');
            btn.textContent = this.isActive ? 'Stop' : 'Start';
            btn.className = this.isActive ? 'ema-btn ema-btn-secondary' : 'ema-btn ema-btn-primary';

            this.log(`Solver ${this.isActive ? 'started' : 'stopped'}`, this.isActive ? 'success' : 'info');
            this.updateGUI();
        }

        toggleSettings() {
            const settings = this.gui.querySelector('#ema-settings');
            settings.style.display = settings.style.display === 'none' ? 'block' : 'none';
        }

        saveSettings() {
            // Save all current settings
            GM_setValue('ocr_confidence', CONFIG.OCR_CONFIDENCE_THRESHOLD);
            GM_setValue('pattern_match', CONFIG.PATTERN_MATCH_THRESHOLD);
            GM_setValue('auto_submit', CONFIG.AUTO_SUBMIT);

            this.log('Settings saved successfully', 'success');
            GM_notification({
                title: 'EMA Captcha Solver',
                text: 'Settings saved successfully!',
                timeout: 3000
            });
        }

        updateGUI() {
            const status = this.gui.querySelector('#ema-status');
            const solved = this.gui.querySelector('#ema-solved');
            const failed = this.gui.querySelector('#ema-failed');
            const indicator = this.gui.querySelector('.ema-status-indicator');

            solved.textContent = this.solvedCount;
            failed.textContent = this.failedCount;

            if (this.isActive) {
                status.textContent = this.currentTask ? 'Solving captcha...' : 'Monitoring for captchas';
                indicator.style.background = this.currentTask ? '#f59e0b' : '#4ade80';
            } else {
                status.textContent = 'Solver stopped';
                indicator.style.background = '#6b7280';
            }

            this.updateLog();
        }

        updateLog() {
            const logContainer = this.gui.querySelector('#ema-log');
            logContainer.innerHTML = this.logs.slice(-20).map(log =>
                `<div class="ema-log-entry ema-log-${log.type}">[${log.time}] ${log.message}</div>`
            ).join('');
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        log(message, type = 'info') {
            const time = new Date().toLocaleTimeString();
            this.logs.push({ message, type, time });
            if (CONFIG.DEBUG) {
                console.log(`[EMA Captcha Solver] ${message}`);
            }
            this.updateLog();
        }

        startMonitoring() {
            setInterval(() => {
                if (this.isActive && !this.currentTask) {
                    this.detectAndSolveCaptcha();
                }
            }, CONFIG.DETECTION_INTERVAL);
        }

        async detectAndSolveCaptcha() {
            try {
                // Detect GetSwift.gg specific captchas and checkpoints
                if (window.location.hostname.includes('getswift.gg')) {
                    await this.solveGetSwiftCaptcha();
                    return;
                }

                // Detect text-based captchas (images with text)
                const textCaptchas = document.querySelectorAll('img[src*="captcha"], img[alt*="captcha"], img[id*="captcha"], .captcha img');
                for (const img of textCaptchas) {
                    if (!img.getAttribute('data-solved')) {
                        await this.solveTextCaptcha(img);
                        return;
                    }
                }

                // Detect math captchas
                const mathCaptchas = document.querySelectorAll('img[src*="math"], .math-captcha img, [class*="math"] img');
                for (const img of mathCaptchas) {
                    if (!img.getAttribute('data-solved')) {
                        await this.solveMathCaptcha(img);
                        return;
                    }
                }

                // Detect simple checkbox captchas
                const checkboxCaptchas = document.querySelectorAll('input[type="checkbox"][name*="captcha"], .captcha-checkbox');
                for (const checkbox of checkboxCaptchas) {
                    if (!checkbox.checked && !checkbox.getAttribute('data-solved')) {
                        await this.solveCheckboxCaptcha(checkbox);
                        return;
                    }
                }

                // Detect slider captchas
                const sliderCaptchas = document.querySelectorAll('.slider-captcha, [class*="slider"] [class*="captcha"]');
                for (const slider of sliderCaptchas) {
                    if (!slider.getAttribute('data-solved')) {
                        await this.solveSliderCaptcha(slider);
                        return;
                    }
                }

                // Detect pattern/puzzle captchas
                const puzzleCaptchas = document.querySelectorAll('.puzzle-captcha, [class*="puzzle"], [class*="jigsaw"]');
                for (const puzzle of puzzleCaptchas) {
                    if (!puzzle.getAttribute('data-solved')) {
                        await this.solvePuzzleCaptcha(puzzle);
                        return;
                    }
                }

            } catch (error) {
                this.log(`Detection error: ${error.message}`, 'error');
            }
        }

        async solveTextCaptcha(imgElement) {
            this.currentTask = 'text-captcha';
            this.log('Detected text captcha, analyzing...', 'info');
            this.updateGUI();

            try {
                // Create canvas for image processing
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Load image
                await this.loadImageToCanvas(imgElement, canvas, ctx);

                // Apply image preprocessing
                const processedImageData = this.preprocessImage(ctx, canvas);

                // Extract text using custom OCR
                const extractedText = await this.performOCR(processedImageData, canvas);

                if (extractedText && extractedText.confidence > CONFIG.OCR_CONFIDENCE_THRESHOLD) {
                    // Find input field for this captcha
                    const inputField = this.findCaptchaInput(imgElement);

                    if (inputField) {
                        inputField.value = extractedText.text;
                        inputField.dispatchEvent(new Event('input', { bubbles: true }));
                        inputField.dispatchEvent(new Event('change', { bubbles: true }));

                        imgElement.setAttribute('data-solved', 'true');
                        this.solvedCount++;
                        this.log(`Text captcha solved: "${extractedText.text}"`, 'success');

                        if (CONFIG.AUTO_SUBMIT) {
                            setTimeout(() => this.autoSubmitForm(), CONFIG.SUBMIT_DELAY);
                        }
                    }
                } else {
                    this.failedCount++;
                    this.log('Text extraction failed or low confidence', 'warning');
                }
            } catch (error) {
                this.failedCount++;
                this.log(`Text captcha solve failed: ${error.message}`, 'error');
            } finally {
                this.currentTask = null;
                this.updateGUI();
            }
        }

        async solveMathCaptcha(imgElement) {
            this.currentTask = 'math-captcha';
            this.log('Detected math captcha, calculating...', 'info');
            this.updateGUI();

            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                await this.loadImageToCanvas(imgElement, canvas, ctx);
                const processedImageData = this.preprocessImage(ctx, canvas);
                const extractedText = await this.performOCR(processedImageData, canvas);

                if (extractedText && extractedText.confidence > CONFIG.OCR_CONFIDENCE_THRESHOLD) {
                    // Parse and solve math expression
                    const mathResult = this.solveMathExpression(extractedText.text);

                    if (mathResult !== null) {
                        const inputField = this.findCaptchaInput(imgElement);

                        if (inputField) {
                            inputField.value = mathResult.toString();
                            inputField.dispatchEvent(new Event('input', { bubbles: true }));
                            inputField.dispatchEvent(new Event('change', { bubbles: true }));

                            imgElement.setAttribute('data-solved', 'true');
                            this.solvedCount++;
                            this.log(`Math captcha solved: ${extractedText.text} = ${mathResult}`, 'success');

                            if (CONFIG.AUTO_SUBMIT) {
                                setTimeout(() => this.autoSubmitForm(), CONFIG.SUBMIT_DELAY);
                            }
                        }
                    } else {
                        this.failedCount++;
                        this.log('Math expression parsing failed', 'warning');
                    }
                } else {
                    this.failedCount++;
                    this.log('Math text extraction failed', 'warning');
                }
            } catch (error) {
                this.failedCount++;
                this.log(`Math captcha solve failed: ${error.message}`, 'error');
            } finally {
                this.currentTask = null;
                this.updateGUI();
            }
        }

        async solveCheckboxCaptcha(checkboxElement) {
            this.currentTask = 'checkbox-captcha';
            this.log('Detected checkbox captcha, analyzing...', 'info');
            this.updateGUI();

            try {
                // Simple checkbox captcha - just check it
                checkboxElement.checked = true;
                checkboxElement.dispatchEvent(new Event('change', { bubbles: true }));
                checkboxElement.dispatchEvent(new Event('click', { bubbles: true }));

                checkboxElement.setAttribute('data-solved', 'true');
                this.solvedCount++;
                this.log('Checkbox captcha solved', 'success');

                if (CONFIG.AUTO_SUBMIT) {
                    setTimeout(() => this.autoSubmitForm(), CONFIG.SUBMIT_DELAY);
                }
            } catch (error) {
                this.failedCount++;
                this.log(`Checkbox captcha solve failed: ${error.message}`, 'error');
            } finally {
                this.currentTask = null;
                this.updateGUI();
            }
        }

        async solveSliderCaptcha(sliderElement) {
            this.currentTask = 'slider-captcha';
            this.log('Detected slider captcha, solving...', 'info');
            this.updateGUI();

            try {
                // Find slider handle and track
                const sliderHandle = sliderElement.querySelector('.slider-handle, [class*="handle"], [class*="thumb"]') || sliderElement;
                const sliderTrack = sliderElement.querySelector('.slider-track, [class*="track"]') || sliderElement.parentElement;

                if (sliderHandle && sliderTrack) {
                    // Calculate slider movement
                    const trackRect = sliderTrack.getBoundingClientRect();
                    const targetPosition = trackRect.width * 0.8; // Move to 80% of track

                    // Simulate drag movement
                    this.simulateSliderDrag(sliderHandle, targetPosition);

                    sliderElement.setAttribute('data-solved', 'true');
                    this.solvedCount++;
                    this.log('Slider captcha solved', 'success');

                    if (CONFIG.AUTO_SUBMIT) {
                        setTimeout(() => this.autoSubmitForm(), CONFIG.SUBMIT_DELAY);
                    }
                } else {
                    this.failedCount++;
                    this.log('Slider elements not found', 'warning');
                }
            } catch (error) {
                this.failedCount++;
                this.log(`Slider captcha solve failed: ${error.message}`, 'error');
            } finally {
                this.currentTask = null;
                this.updateGUI();
            }
        }

        async solvePuzzleCaptcha(puzzleElement) {
            this.currentTask = 'puzzle-captcha';
            this.log('Detected puzzle captcha, analyzing...', 'info');
            this.updateGUI();

            try {
                // Find puzzle pieces and target area
                const puzzlePieces = puzzleElement.querySelectorAll('.puzzle-piece, [class*="piece"]');
                const targetArea = puzzleElement.querySelector('.puzzle-target, [class*="target"]');

                if (puzzlePieces.length > 0) {
                    // Simple puzzle solving - try to fit pieces
                    for (const piece of puzzlePieces) {
                        this.simulatePuzzlePieceMove(piece, targetArea || puzzleElement);
                        await this.sleep(200); // Small delay between moves
                    }

                    puzzleElement.setAttribute('data-solved', 'true');
                    this.solvedCount++;
                    this.log('Puzzle captcha solved', 'success');

                    if (CONFIG.AUTO_SUBMIT) {
                        setTimeout(() => this.autoSubmitForm(), CONFIG.SUBMIT_DELAY);
                    }
                } else {
                    this.failedCount++;
                    this.log('Puzzle pieces not found', 'warning');
                }
            } catch (error) {
                this.failedCount++;
                this.log(`Puzzle captcha solve failed: ${error.message}`, 'error');
            } finally {
                this.currentTask = null;
                this.updateGUI();
            }
        }

        async solveGetSwiftCaptcha() {
            this.currentTask = 'getswift-captcha';
            this.log('Detected GetSwift.gg checkpoint, analyzing...', 'info');
            this.updateGUI();

            try {
                // Check if already solved
                if (document.querySelector('[data-getswift-solved="true"]')) {
                    return;
                }

                // Method 1: Look for direct bypass buttons or links
                const bypassElements = document.querySelectorAll(
                    'button[onclick*="bypass"], button[onclick*="skip"], ' +
                    'a[href*="bypass"], a[href*="skip"], ' +
                    '.bypass-btn, .skip-btn, #bypass, #skip, ' +
                    'button:contains("Skip"), button:contains("Continue"), ' +
                    'input[value*="Continue"], input[value*="Skip"]'
                );

                for (const element of bypassElements) {
                    if (element.offsetParent !== null) { // Check if visible
                        this.log('Found bypass button, clicking...', 'info');
                        element.click();
                        document.body.setAttribute('data-getswift-solved', 'true');
                        this.solvedCount++;
                        this.log('GetSwift checkpoint bypassed via button!', 'success');
                        return;
                    }
                }

                // Method 2: Look for checkpoint forms with hidden inputs
                const forms = document.querySelectorAll('form');
                for (const form of forms) {
                    const hiddenInputs = form.querySelectorAll('input[type="hidden"]');
                    const submitBtn = form.querySelector('input[type="submit"], button[type="submit"], button');

                    if (hiddenInputs.length > 0 && submitBtn) {
                        this.log('Found checkpoint form, submitting...', 'info');

                        // Fill any required visible inputs
                        const textInputs = form.querySelectorAll('input[type="text"], input[type="email"]');
                        for (const input of textInputs) {
                            if (!input.value && input.name) {
                                // Generate appropriate values based on input name/type
                                if (input.name.includes('email')) {
                                    input.value = '<EMAIL>';
                                } else if (input.name.includes('name')) {
                                    input.value = 'User';
                                } else {
                                    input.value = 'test';
                                }
                                input.dispatchEvent(new Event('input', { bubbles: true }));
                            }
                        }

                        submitBtn.click();
                        document.body.setAttribute('data-getswift-solved', 'true');
                        this.solvedCount++;
                        this.log('GetSwift checkpoint form submitted!', 'success');
                        return;
                    }
                }

                // Method 3: Look for timer-based checkpoints
                const timerElements = document.querySelectorAll('.timer, #timer, [class*="countdown"], [id*="countdown"]');
                if (timerElements.length > 0) {
                    this.log('Found timer checkpoint, attempting bypass...', 'info');

                    // Try to manipulate timer
                    for (const timer of timerElements) {
                        timer.textContent = '0';
                        timer.style.display = 'none';
                    }

                    // Look for continue button that appears after timer
                    setTimeout(() => {
                        const continueBtn = document.querySelector(
                            'button:not([disabled]), input[type="submit"]:not([disabled]), ' +
                            '.continue-btn:not([disabled]), #continue:not([disabled])'
                        );
                        if (continueBtn) {
                            continueBtn.click();
                            this.log('Timer bypassed and continued!', 'success');
                        }
                    }, 1000);

                    document.body.setAttribute('data-getswift-solved', 'true');
                    this.solvedCount++;
                    return;
                }

                // Method 4: Advanced GetSwift-specific bypasses
                await this.advancedGetSwiftBypass();

                // Method 5: Look for JavaScript-based checkpoints
                await this.bypassJavaScriptCheckpoint();

                // Method 5: Look for iframe-based captchas
                const iframes = document.querySelectorAll('iframe');
                for (const iframe of iframes) {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        if (iframeDoc) {
                            const captchaImg = iframeDoc.querySelector('img[src*="captcha"], .captcha img');
                            if (captchaImg) {
                                await this.solveTextCaptcha(captchaImg);
                                return;
                            }
                        }
                    } catch (e) {
                        // Cross-origin iframe, try different approach
                        this.log('Cross-origin iframe detected, trying alternative method...', 'info');
                    }
                }

                // Method 6: Auto-click any visible "Continue" or "Proceed" buttons
                const proceedButtons = document.querySelectorAll(
                    'button, input[type="button"], input[type="submit"], a'
                );

                for (const btn of proceedButtons) {
                    const text = (btn.textContent || btn.value || btn.title || '').toLowerCase();
                    if (text.includes('continue') || text.includes('proceed') ||
                        text.includes('next') || text.includes('verify') ||
                        text.includes('confirm') || text.includes('submit')) {

                        if (btn.offsetParent !== null && !btn.disabled) {
                            this.log(`Clicking "${text}" button...`, 'info');
                            btn.click();
                            document.body.setAttribute('data-getswift-solved', 'true');
                            this.solvedCount++;
                            this.log('GetSwift checkpoint completed!', 'success');
                            return;
                        }
                    }
                }

                // Method 7: Try to detect and solve any remaining captcha images
                const allImages = document.querySelectorAll('img');
                for (const img of allImages) {
                    if (img.src && (img.width > 50 && img.height > 20)) {
                        // Potential captcha image
                        await this.solveTextCaptcha(img);
                        return;
                    }
                }

                this.log('No solvable GetSwift checkpoint found', 'warning');

            } catch (error) {
                this.failedCount++;
                this.log(`GetSwift captcha solve failed: ${error.message}`, 'error');
            } finally {
                this.currentTask = null;
                this.updateGUI();
            }
        }

        async advancedGetSwiftBypass() {
            try {
                this.log('Attempting advanced GetSwift bypass techniques...', 'info');

                // Method 1: URL manipulation for direct access
                const currentUrl = window.location.href;
                if (currentUrl.includes('/checkpoint/')) {
                    // Try to extract key and redirect
                    const keyMatch = currentUrl.match(/\/([a-zA-Z0-9]+)$/);
                    if (keyMatch) {
                        const key = keyMatch[1];
                        const directUrl = currentUrl.replace('/checkpoint/', '/direct/') + `?key=${key}&bypass=1`;
                        this.log(`Attempting direct access with key: ${directUrl}`, 'info');

                        // Try to navigate directly
                        setTimeout(() => {
                            window.location.href = directUrl;
                        }, 1000);
                    }
                }

                // Method 2: Cookie manipulation
                document.cookie = 'checkpoint_passed=true; path=/';
                document.cookie = 'verification_complete=1; path=/';
                document.cookie = 'human_verified=true; path=/';

                // Method 3: LocalStorage manipulation
                if (window.localStorage) {
                    localStorage.setItem('checkpoint_status', 'completed');
                    localStorage.setItem('verification_passed', 'true');
                    localStorage.setItem('user_verified', '1');
                }

                // Method 4: SessionStorage manipulation
                if (window.sessionStorage) {
                    sessionStorage.setItem('checkpoint_bypass', 'true');
                    sessionStorage.setItem('verification_token', 'valid');
                }

                // Method 5: Override common anti-bot functions
                if (window.navigator) {
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => false,
                    });
                }

                // Method 6: Simulate human-like behavior
                this.simulateHumanBehavior();

                // Method 7: Auto-click any hidden or invisible continue elements
                const hiddenElements = document.querySelectorAll('[style*="display: none"], [hidden], .hidden');
                for (const element of hiddenElements) {
                    if (element.tagName === 'BUTTON' || element.tagName === 'A') {
                        const text = element.textContent.toLowerCase();
                        if (text.includes('continue') || text.includes('proceed') || text.includes('next')) {
                            element.style.display = 'block';
                            element.hidden = false;
                            element.classList.remove('hidden');
                            setTimeout(() => element.click(), 100);
                            this.log('Clicked hidden continue element', 'success');
                            return true;
                        }
                    }
                }

                // Method 8: Force-enable disabled buttons
                const disabledButtons = document.querySelectorAll('button[disabled], input[disabled]');
                for (const button of disabledButtons) {
                    button.disabled = false;
                    button.removeAttribute('disabled');
                    const text = (button.textContent || button.value || '').toLowerCase();
                    if (text.includes('continue') || text.includes('proceed') || text.includes('verify')) {
                        setTimeout(() => button.click(), 200);
                        this.log('Enabled and clicked disabled button', 'success');
                        return true;
                    }
                }

                // Method 9: Trigger completion events
                const completionEvents = [
                    'checkpoint:complete',
                    'verification:success',
                    'captcha:solved',
                    'human:verified'
                ];

                for (const eventName of completionEvents) {
                    document.dispatchEvent(new CustomEvent(eventName, { detail: { success: true } }));
                    window.dispatchEvent(new CustomEvent(eventName, { detail: { success: true } }));
                }

                return false;
            } catch (error) {
                this.log(`Advanced bypass error: ${error.message}`, 'error');
                return false;
            }
        }

        simulateHumanBehavior() {
            // Simulate mouse movements
            const mouseMoveEvent = new MouseEvent('mousemove', {
                clientX: Math.random() * window.innerWidth,
                clientY: Math.random() * window.innerHeight,
                bubbles: true
            });
            document.dispatchEvent(mouseMoveEvent);

            // Simulate scroll
            window.scrollBy(0, Math.random() * 100 - 50);

            // Simulate key press
            const keyEvent = new KeyboardEvent('keydown', {
                key: 'Tab',
                bubbles: true
            });
            document.dispatchEvent(keyEvent);

            // Focus on page
            window.focus();
            document.body.focus();
        }

        async bypassJavaScriptCheckpoint() {
            // Common JavaScript checkpoint bypass techniques
            try {
                // Method 1: Override common checkpoint functions
                if (window.setTimeout) {
                    const originalSetTimeout = window.setTimeout;
                    window.setTimeout = function(callback, delay) {
                        if (delay > 5000) { // Skip long delays
                            return originalSetTimeout(callback, 100);
                        }
                        return originalSetTimeout(callback, delay);
                    };
                }

                // Method 2: Trigger common checkpoint events
                const events = ['load', 'DOMContentLoaded', 'ready'];
                for (const eventName of events) {
                    document.dispatchEvent(new Event(eventName));
                    window.dispatchEvent(new Event(eventName));
                }

                // Method 3: Look for and execute checkpoint completion functions
                const commonFunctions = [
                    'completeCheckpoint', 'finishVerification', 'proceedNext',
                    'continueProcess', 'validateUser', 'confirmHuman'
                ];

                for (const funcName of commonFunctions) {
                    if (typeof window[funcName] === 'function') {
                        this.log(`Executing ${funcName}()...`, 'info');
                        window[funcName]();
                        return true;
                    }
                }

                // Method 4: Auto-fill and submit any forms
                const forms = document.querySelectorAll('form:not([data-processed])');
                for (const form of forms) {
                    form.setAttribute('data-processed', 'true');

                    // Auto-fill required fields
                    const requiredInputs = form.querySelectorAll('input[required], select[required]');
                    for (const input of requiredInputs) {
                        if (input.type === 'email') {
                            input.value = '<EMAIL>';
                        } else if (input.type === 'text') {
                            input.value = 'test';
                        } else if (input.tagName === 'SELECT') {
                            if (input.options.length > 1) {
                                input.selectedIndex = 1;
                            }
                        }
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                    }

                    // Submit form
                    const submitBtn = form.querySelector('input[type="submit"], button[type="submit"]');
                    if (submitBtn) {
                        setTimeout(() => submitBtn.click(), 500);
                        return true;
                    }
                }

                return false;
            } catch (error) {
                this.log(`JavaScript bypass error: ${error.message}`, 'error');
                return false;
            }
        }

        // Computer Vision and OCR Methods
        async loadImageToCanvas(imgElement, canvas, ctx) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.crossOrigin = 'anonymous';

                img.onload = () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    resolve();
                };

                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = imgElement.src;
            });
        }

        preprocessImage(ctx, canvas) {
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Convert to grayscale and apply threshold
            for (let i = 0; i < data.length; i += 4) {
                const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
                const threshold = gray > 128 ? 255 : 0;

                data[i] = threshold;     // Red
                data[i + 1] = threshold; // Green
                data[i + 2] = threshold; // Blue
                // Alpha stays the same
            }

            ctx.putImageData(imageData, 0, 0);
            return imageData;
        }

        async performOCR(imageData, canvas) {
            // Custom OCR implementation using pattern recognition
            const text = this.extractTextFromImage(imageData, canvas);
            const confidence = this.calculateOCRConfidence(text, imageData);

            return {
                text: text.trim(),
                confidence: confidence
            };
        }

        extractTextFromImage(imageData, canvas) {
            // Simple character recognition patterns
            const patterns = {
                '0': [[1,1,1],[1,0,1],[1,0,1],[1,0,1],[1,1,1]],
                '1': [[0,1,0],[1,1,0],[0,1,0],[0,1,0],[1,1,1]],
                '2': [[1,1,1],[0,0,1],[1,1,1],[1,0,0],[1,1,1]],
                '3': [[1,1,1],[0,0,1],[1,1,1],[0,0,1],[1,1,1]],
                '4': [[1,0,1],[1,0,1],[1,1,1],[0,0,1],[0,0,1]],
                '5': [[1,1,1],[1,0,0],[1,1,1],[0,0,1],[1,1,1]],
                '6': [[1,1,1],[1,0,0],[1,1,1],[1,0,1],[1,1,1]],
                '7': [[1,1,1],[0,0,1],[0,0,1],[0,0,1],[0,0,1]],
                '8': [[1,1,1],[1,0,1],[1,1,1],[1,0,1],[1,1,1]],
                '9': [[1,1,1],[1,0,1],[1,1,1],[0,0,1],[1,1,1]],
                'A': [[0,1,0],[1,0,1],[1,1,1],[1,0,1],[1,0,1]],
                'B': [[1,1,0],[1,0,1],[1,1,0],[1,0,1],[1,1,0]],
                'C': [[0,1,1],[1,0,0],[1,0,0],[1,0,0],[0,1,1]],
                'D': [[1,1,0],[1,0,1],[1,0,1],[1,0,1],[1,1,0]],
                'E': [[1,1,1],[1,0,0],[1,1,0],[1,0,0],[1,1,1]],
                'F': [[1,1,1],[1,0,0],[1,1,0],[1,0,0],[1,0,0]]
            };

            // Extract characters using pattern matching
            const characters = this.segmentCharacters(imageData, canvas);
            let recognizedText = '';

            for (const charData of characters) {
                const bestMatch = this.matchCharacterPattern(charData, patterns);
                if (bestMatch.confidence > CONFIG.PATTERN_MATCH_THRESHOLD) {
                    recognizedText += bestMatch.character;
                }
            }

            return recognizedText;
        }

        segmentCharacters(imageData, canvas) {
            // Simple character segmentation based on connected components
            const data = imageData.data;
            const width = canvas.width;
            const height = canvas.height;
            const characters = [];

            // Find character boundaries
            let inChar = false;
            let charStart = 0;

            for (let x = 0; x < width; x++) {
                let hasPixel = false;

                for (let y = 0; y < height; y++) {
                    const index = (y * width + x) * 4;
                    if (data[index] === 0) { // Black pixel
                        hasPixel = true;
                        break;
                    }
                }

                if (hasPixel && !inChar) {
                    charStart = x;
                    inChar = true;
                } else if (!hasPixel && inChar) {
                    // Extract character region
                    const charWidth = x - charStart;
                    if (charWidth > 3) { // Minimum character width
                        characters.push(this.extractCharacterRegion(imageData, charStart, 0, charWidth, height));
                    }
                    inChar = false;
                }
            }

            return characters;
        }

        extractCharacterRegion(imageData, x, y, width, height) {
            const data = imageData.data;
            const imgWidth = imageData.width;
            const charData = [];

            for (let cy = y; cy < y + height; cy++) {
                const row = [];
                for (let cx = x; cx < x + width; cx++) {
                    const index = (cy * imgWidth + cx) * 4;
                    row.push(data[index] === 0 ? 1 : 0); // 1 for black, 0 for white
                }
                charData.push(row);
            }

            return charData;
        }

        matchCharacterPattern(charData, patterns) {
            let bestMatch = { character: '', confidence: 0 };

            for (const [char, pattern] of Object.entries(patterns)) {
                const confidence = this.calculatePatternSimilarity(charData, pattern);
                if (confidence > bestMatch.confidence) {
                    bestMatch = { character: char, confidence };
                }
            }

            return bestMatch;
        }

        calculatePatternSimilarity(charData, pattern) {
            if (!charData || charData.length === 0) return 0;

            // Resize character data to match pattern size
            const resized = this.resizeCharacterData(charData, pattern[0].length, pattern.length);

            let matches = 0;
            let total = 0;

            for (let y = 0; y < pattern.length; y++) {
                for (let x = 0; x < pattern[y].length; x++) {
                    if (resized[y] && resized[y][x] !== undefined) {
                        if (resized[y][x] === pattern[y][x]) {
                            matches++;
                        }
                        total++;
                    }
                }
            }

            return total > 0 ? matches / total : 0;
        }

        resizeCharacterData(charData, targetWidth, targetHeight) {
            const sourceHeight = charData.length;
            const sourceWidth = charData[0] ? charData[0].length : 0;

            if (sourceWidth === 0 || sourceHeight === 0) return [];

            const resized = [];

            for (let y = 0; y < targetHeight; y++) {
                const row = [];
                for (let x = 0; x < targetWidth; x++) {
                    const sourceY = Math.floor((y / targetHeight) * sourceHeight);
                    const sourceX = Math.floor((x / targetWidth) * sourceWidth);
                    row.push(charData[sourceY] ? charData[sourceY][sourceX] || 0 : 0);
                }
                resized.push(row);
            }

            return resized;
        }

        calculateOCRConfidence(text, imageData) {
            // Simple confidence calculation based on text length and image quality
            if (!text || text.length === 0) return 0;

            const maxLength = 8;
            const lengthScore = Math.min(text.length / maxLength, 1);
            const qualityScore = this.assessImageQuality(imageData);

            return (lengthScore + qualityScore) / 2;
        }

        assessImageQuality(imageData) {
            // Assess image quality based on contrast and noise
            const data = imageData.data;
            let blackPixels = 0;
            let whitePixels = 0;

            for (let i = 0; i < data.length; i += 4) {
                if (data[i] === 0) blackPixels++;
                else whitePixels++;
            }

            const totalPixels = blackPixels + whitePixels;
            const contrast = Math.abs(blackPixels - whitePixels) / totalPixels;

            return Math.min(contrast * 2, 1); // Normalize to 0-1
        }

        // Utility Methods
        solveMathExpression(expression) {
            try {
                // Clean and parse math expression
                const cleanExpr = expression.replace(/[^0-9+\-*/().\s]/g, '');

                // Simple math evaluation (safe)
                const operators = {
                    '+': (a, b) => a + b,
                    '-': (a, b) => a - b,
                    '*': (a, b) => a * b,
                    '/': (a, b) => a / b
                };

                // Parse simple expressions like "2 + 3" or "5 * 4"
                const match = cleanExpr.match(/(\d+)\s*([+\-*/])\s*(\d+)/);
                if (match) {
                    const [, num1, operator, num2] = match;
                    const a = parseInt(num1);
                    const b = parseInt(num2);

                    if (operators[operator]) {
                        return Math.round(operators[operator](a, b));
                    }
                }

                return null;
            } catch (error) {
                this.log(`Math parsing error: ${error.message}`, 'error');
                return null;
            }
        }

        findCaptchaInput(imgElement) {
            // Find input field associated with captcha image
            const parent = imgElement.closest('form, .captcha, [class*="captcha"]');

            if (parent) {
                // Look for input fields in the same container
                const inputs = parent.querySelectorAll('input[type="text"], input[type="password"], input:not([type])');
                for (const input of inputs) {
                    if (!input.value && !input.disabled) {
                        return input;
                    }
                }
            }

            // Fallback: look for nearby input fields
            const nearbyInputs = document.querySelectorAll('input[type="text"], input[type="password"], input:not([type])');
            for (const input of nearbyInputs) {
                if (!input.value && !input.disabled) {
                    const rect1 = imgElement.getBoundingClientRect();
                    const rect2 = input.getBoundingClientRect();
                    const distance = Math.abs(rect1.top - rect2.top) + Math.abs(rect1.left - rect2.left);

                    if (distance < 200) { // Within 200px
                        return input;
                    }
                }
            }

            return null;
        }

        simulateSliderDrag(handle, targetPosition) {
            const rect = handle.getBoundingClientRect();
            const startX = rect.left + rect.width / 2;
            const startY = rect.top + rect.height / 2;
            const endX = startX + targetPosition;

            // Simulate mouse events
            this.dispatchMouseEvent(handle, 'mousedown', startX, startY);

            // Simulate drag movement
            const steps = 10;
            for (let i = 1; i <= steps; i++) {
                const currentX = startX + (targetPosition * i / steps);
                this.dispatchMouseEvent(handle, 'mousemove', currentX, startY);
            }

            this.dispatchMouseEvent(handle, 'mouseup', endX, startY);
        }

        simulatePuzzlePieceMove(piece, target) {
            const pieceRect = piece.getBoundingClientRect();
            const targetRect = target.getBoundingClientRect();

            const startX = pieceRect.left + pieceRect.width / 2;
            const startY = pieceRect.top + pieceRect.height / 2;
            const endX = targetRect.left + targetRect.width / 2;
            const endY = targetRect.top + targetRect.height / 2;

            this.dispatchMouseEvent(piece, 'mousedown', startX, startY);
            this.dispatchMouseEvent(piece, 'mousemove', endX, endY);
            this.dispatchMouseEvent(piece, 'mouseup', endX, endY);
        }

        dispatchMouseEvent(element, type, clientX, clientY) {
            const event = new MouseEvent(type, {
                bubbles: true,
                cancelable: true,
                clientX: clientX,
                clientY: clientY
            });
            element.dispatchEvent(event);
        }

        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        updateProgress(percentage) {
            const progressBar = this.gui.querySelector('#ema-progress');
            if (progressBar) {
                progressBar.style.width = `${percentage}%`;
            }
        }

        autoSubmitForm() {
            if (!CONFIG.AUTO_SUBMIT) return;

            // Look for submit buttons and forms
            const submitBtn = document.querySelector('input[type="submit"], button[type="submit"], .submit-btn, #submit');
            const form = document.querySelector('form');

            if (submitBtn && !submitBtn.disabled) {
                this.log('Auto-submitting form...', 'info');
                setTimeout(() => {
                    submitBtn.click();
                }, CONFIG.SUBMIT_DELAY);
            } else if (form) {
                this.log('Auto-submitting form...', 'info');
                setTimeout(() => {
                    form.submit();
                }, CONFIG.SUBMIT_DELAY);
            }
        }
    }

    // Initialize the captcha solver
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            new CaptchaSolver();
        });
    } else {
        new CaptchaSolver();
    }

})();
