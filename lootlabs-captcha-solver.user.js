// ==UserScript==
// @name         LootLabs Captcha Solver Pro
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  Advanced captcha solver for LootLabs with modern GUI and real-time functionality
// <AUTHOR>
// @match        https://*.lootlabs.com/*
// @match        https://lootlabs.com/*
// @match        https://*.hcaptcha.com/*captcha*
// @match        https://*.recaptcha.net/*
// @match        https://*.google.com/recaptcha/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_notification
// @connect      2captcha.com
// @connect      api.2captcha.com
// @run-at       document-end
// @updateURL    https://raw.githubusercontent.com/your-repo/lootlabs-captcha-solver/main/lootlabs-captcha-solver.user.js
// @downloadURL  https://raw.githubusercontent.com/your-repo/lootlabs-captcha-solver/main/lootlabs-captcha-solver.user.js
// ==/UserScript==

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        API_KEY: GM_getValue('captcha_api_key', ''), // Get from 2captcha.com
        API_BASE_URL: 'https://2captcha.com',
        POLLING_INTERVAL: 3000,
        MAX_RETRIES: 10,
        DEBUG: true
    };

    // Modern CSS Styles
    const STYLES = `
        .ema-captcha-solver {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 320px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            z-index: 999999;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .ema-header {
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .ema-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ema-status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4ade80;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .ema-body {
            padding: 20px;
        }

        .ema-status {
            font-size: 14px;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            border-left: 4px solid #4ade80;
        }

        .ema-progress {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .ema-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4ade80, #22d3ee);
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .ema-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .ema-stat {
            text-align: center;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }

        .ema-stat-value {
            font-size: 20px;
            font-weight: 600;
            display: block;
        }

        .ema-stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .ema-controls {
            display: flex;
            gap: 10px;
        }

        .ema-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .ema-btn-primary {
            background: #4ade80;
            color: white;
        }

        .ema-btn-primary:hover {
            background: #22c55e;
            transform: translateY(-2px);
        }

        .ema-btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .ema-btn-secondary:hover {
            background: rgba(255,255,255,0.3);
        }

        .ema-settings {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .ema-input {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .ema-input::placeholder {
            color: rgba(255,255,255,0.6);
        }

        .ema-log {
            max-height: 150px;
            overflow-y: auto;
            background: rgba(0,0,0,0.2);
            border-radius: 8px;
            padding: 10px;
            font-size: 12px;
            font-family: 'Courier New', monospace;
            margin-top: 10px;
        }

        .ema-log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .ema-log-success { color: #4ade80; }
        .ema-log-error { color: #ef4444; }
        .ema-log-info { color: #60a5fa; }
        .ema-log-warning { color: #f59e0b; }

        .ema-minimized {
            width: 60px;
            height: 60px;
        }

        .ema-minimized .ema-body,
        .ema-minimized .ema-header {
            display: none;
        }

        .ema-toggle {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            opacity: 0.7;
        }

        .ema-toggle:hover {
            opacity: 1;
        }
    `;

    // Captcha Solver Class
    class CaptchaSolver {
        constructor() {
            this.isActive = false;
            this.solvedCount = 0;
            this.failedCount = 0;
            this.currentTask = null;
            this.gui = null;
            this.logs = [];

            this.init();
        }

        init() {
            this.addStyles();
            this.createGUI();
            this.startMonitoring();
            this.log('Captcha Solver initialized', 'success');
        }

        addStyles() {
            GM_addStyle(STYLES);
        }

        createGUI() {
            this.gui = document.createElement('div');
            this.gui.className = 'ema-captcha-solver';
            this.gui.innerHTML = this.getGUIHTML();
            document.body.appendChild(this.gui);

            this.bindEvents();
            this.updateGUI();
        }

        getGUIHTML() {
            return `
                <button class="ema-toggle" onclick="this.parentElement.classList.toggle('ema-minimized')">−</button>
                <div class="ema-header">
                    <h3 class="ema-title">
                        <div class="ema-status-indicator"></div>
                        EMA Captcha Solver
                    </h3>
                </div>
                <div class="ema-body">
                    <div class="ema-status" id="ema-status">Ready to solve captchas</div>
                    <div class="ema-progress">
                        <div class="ema-progress-bar" id="ema-progress"></div>
                    </div>
                    <div class="ema-stats">
                        <div class="ema-stat">
                            <span class="ema-stat-value" id="ema-solved">0</span>
                            <span class="ema-stat-label">Solved</span>
                        </div>
                        <div class="ema-stat">
                            <span class="ema-stat-value" id="ema-failed">0</span>
                            <span class="ema-stat-label">Failed</span>
                        </div>
                    </div>
                    <div class="ema-controls">
                        <button class="ema-btn ema-btn-primary" id="ema-toggle-btn">Start</button>
                        <button class="ema-btn ema-btn-secondary" id="ema-settings-btn">Settings</button>
                    </div>
                    <div class="ema-settings" id="ema-settings" style="display: none;">
                        <input type="text" class="ema-input" id="ema-api-key" placeholder="2Captcha API Key" value="${CONFIG.API_KEY}">
                        <button class="ema-btn ema-btn-primary" id="ema-save-settings">Save Settings</button>
                    </div>
                    <div class="ema-log" id="ema-log"></div>
                </div>
            `;
        }

        bindEvents() {
            const toggleBtn = this.gui.querySelector('#ema-toggle-btn');
            const settingsBtn = this.gui.querySelector('#ema-settings-btn');
            const saveSettingsBtn = this.gui.querySelector('#ema-save-settings');
            const apiKeyInput = this.gui.querySelector('#ema-api-key');

            toggleBtn.addEventListener('click', () => this.toggleSolver());
            settingsBtn.addEventListener('click', () => this.toggleSettings());
            saveSettingsBtn.addEventListener('click', () => this.saveSettings());

            // Auto-save API key on input
            apiKeyInput.addEventListener('input', (e) => {
                CONFIG.API_KEY = e.target.value;
                GM_setValue('captcha_api_key', e.target.value);
            });
        }

        toggleSolver() {
            this.isActive = !this.isActive;
            const btn = this.gui.querySelector('#ema-toggle-btn');
            btn.textContent = this.isActive ? 'Stop' : 'Start';
            btn.className = this.isActive ? 'ema-btn ema-btn-secondary' : 'ema-btn ema-btn-primary';

            this.log(`Solver ${this.isActive ? 'started' : 'stopped'}`, this.isActive ? 'success' : 'info');
            this.updateGUI();
        }

        toggleSettings() {
            const settings = this.gui.querySelector('#ema-settings');
            settings.style.display = settings.style.display === 'none' ? 'block' : 'none';
        }

        saveSettings() {
            const apiKey = this.gui.querySelector('#ema-api-key').value;
            CONFIG.API_KEY = apiKey;
            GM_setValue('captcha_api_key', apiKey);
            this.log('Settings saved successfully', 'success');
            GM_notification({
                title: 'EMA Captcha Solver',
                text: 'Settings saved successfully!',
                timeout: 3000
            });
        }

        updateGUI() {
            const status = this.gui.querySelector('#ema-status');
            const solved = this.gui.querySelector('#ema-solved');
            const failed = this.gui.querySelector('#ema-failed');
            const indicator = this.gui.querySelector('.ema-status-indicator');

            solved.textContent = this.solvedCount;
            failed.textContent = this.failedCount;

            if (this.isActive) {
                status.textContent = this.currentTask ? 'Solving captcha...' : 'Monitoring for captchas';
                indicator.style.background = this.currentTask ? '#f59e0b' : '#4ade80';
            } else {
                status.textContent = 'Solver stopped';
                indicator.style.background = '#6b7280';
            }

            this.updateLog();
        }

        updateLog() {
            const logContainer = this.gui.querySelector('#ema-log');
            logContainer.innerHTML = this.logs.slice(-20).map(log =>
                `<div class="ema-log-entry ema-log-${log.type}">[${log.time}] ${log.message}</div>`
            ).join('');
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        log(message, type = 'info') {
            const time = new Date().toLocaleTimeString();
            this.logs.push({ message, type, time });
            if (CONFIG.DEBUG) {
                console.log(`[EMA Captcha Solver] ${message}`);
            }
            this.updateLog();
        }

        startMonitoring() {
            setInterval(() => {
                if (this.isActive && !this.currentTask) {
                    this.detectAndSolveCaptcha();
                }
            }, 2000);
        }

        async detectAndSolveCaptcha() {
            try {
                // Detect reCAPTCHA v2
                const recaptchaV2 = document.querySelector('.g-recaptcha:not([data-solved="true"])');
                if (recaptchaV2) {
                    await this.solveRecaptchaV2(recaptchaV2);
                    return;
                }

                // Detect reCAPTCHA v3
                const recaptchaV3 = document.querySelector('input[name="g-recaptcha-response"]:not([data-solved="true"])');
                if (recaptchaV3 && !recaptchaV3.value) {
                    await this.solveRecaptchaV3(recaptchaV3);
                    return;
                }

                // Detect hCaptcha
                const hcaptcha = document.querySelector('.h-captcha:not([data-solved="true"])');
                if (hcaptcha) {
                    await this.solveHcaptcha(hcaptcha);
                    return;
                }

                // Detect Turnstile
                const turnstile = document.querySelector('.cf-turnstile:not([data-solved="true"])');
                if (turnstile) {
                    await this.solveTurnstile(turnstile);
                    return;
                }

            } catch (error) {
                this.log(`Detection error: ${error.message}`, 'error');
            }
        }

        async solveRecaptchaV2(element) {
            if (!CONFIG.API_KEY) {
                this.log('API key not configured', 'error');
                return;
            }

            this.currentTask = 'recaptcha-v2';
            this.log('Detected reCAPTCHA v2, solving...', 'info');
            this.updateGUI();

            try {
                const sitekey = element.getAttribute('data-sitekey');
                const pageurl = window.location.href;

                // Submit captcha to 2captcha
                const taskId = await this.submitCaptcha({
                    method: 'userrecaptcha',
                    googlekey: sitekey,
                    pageurl: pageurl
                });

                if (taskId) {
                    const solution = await this.pollForSolution(taskId);
                    if (solution) {
                        // Inject solution
                        const responseElement = document.querySelector('textarea[name="g-recaptcha-response"]');
                        if (responseElement) {
                            responseElement.value = solution;
                            responseElement.style.display = 'block';

                            // Mark as solved
                            element.setAttribute('data-solved', 'true');

                            // Trigger events
                            responseElement.dispatchEvent(new Event('input', { bubbles: true }));
                            responseElement.dispatchEvent(new Event('change', { bubbles: true }));

                            this.solvedCount++;
                            this.log('reCAPTCHA v2 solved successfully!', 'success');

                            // Auto-submit if form exists
                            this.autoSubmitForm();
                        }
                    }
                }
            } catch (error) {
                this.failedCount++;
                this.log(`reCAPTCHA v2 solve failed: ${error.message}`, 'error');
            } finally {
                this.currentTask = null;
                this.updateGUI();
            }
        }

        async solveRecaptchaV3(element) {
            if (!CONFIG.API_KEY) {
                this.log('API key not configured', 'error');
                return;
            }

            this.currentTask = 'recaptcha-v3';
            this.log('Detected reCAPTCHA v3, solving...', 'info');
            this.updateGUI();

            try {
                const sitekey = document.querySelector('[data-sitekey]')?.getAttribute('data-sitekey') ||
                              window.grecaptcha?.enterprise?.sitekey ||
                              '6LfCVLAUAAAAALFwwRnnCJ12DalriUGbj8FW_J39'; // fallback
                const pageurl = window.location.href;
                const action = 'submit'; // default action

                const taskId = await this.submitCaptcha({
                    method: 'userrecaptcha',
                    googlekey: sitekey,
                    pageurl: pageurl,
                    version: 'v3',
                    action: action,
                    min_score: 0.3
                });

                if (taskId) {
                    const solution = await this.pollForSolution(taskId);
                    if (solution) {
                        element.value = solution;
                        element.setAttribute('data-solved', 'true');

                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('change', { bubbles: true }));

                        this.solvedCount++;
                        this.log('reCAPTCHA v3 solved successfully!', 'success');
                        this.autoSubmitForm();
                    }
                }
            } catch (error) {
                this.failedCount++;
                this.log(`reCAPTCHA v3 solve failed: ${error.message}`, 'error');
            } finally {
                this.currentTask = null;
                this.updateGUI();
            }
        }

        async solveHcaptcha(element) {
            if (!CONFIG.API_KEY) {
                this.log('API key not configured', 'error');
                return;
            }

            this.currentTask = 'hcaptcha';
            this.log('Detected hCaptcha, solving...', 'info');
            this.updateGUI();

            try {
                const sitekey = element.getAttribute('data-sitekey');
                const pageurl = window.location.href;

                const taskId = await this.submitCaptcha({
                    method: 'hcaptcha',
                    sitekey: sitekey,
                    pageurl: pageurl
                });

                if (taskId) {
                    const solution = await this.pollForSolution(taskId);
                    if (solution) {
                        const responseElement = document.querySelector('textarea[name="h-captcha-response"]');
                        if (responseElement) {
                            responseElement.value = solution;
                            element.setAttribute('data-solved', 'true');

                            responseElement.dispatchEvent(new Event('input', { bubbles: true }));
                            responseElement.dispatchEvent(new Event('change', { bubbles: true }));

                            this.solvedCount++;
                            this.log('hCaptcha solved successfully!', 'success');
                            this.autoSubmitForm();
                        }
                    }
                }
            } catch (error) {
                this.failedCount++;
                this.log(`hCaptcha solve failed: ${error.message}`, 'error');
            } finally {
                this.currentTask = null;
                this.updateGUI();
            }
        }

        async solveTurnstile(element) {
            if (!CONFIG.API_KEY) {
                this.log('API key not configured', 'error');
                return;
            }

            this.currentTask = 'turnstile';
            this.log('Detected Turnstile, solving...', 'info');
            this.updateGUI();

            try {
                const sitekey = element.getAttribute('data-sitekey');
                const pageurl = window.location.href;

                const taskId = await this.submitCaptcha({
                    method: 'turnstile',
                    sitekey: sitekey,
                    pageurl: pageurl
                });

                if (taskId) {
                    const solution = await this.pollForSolution(taskId);
                    if (solution) {
                        const responseElement = document.querySelector('input[name="cf-turnstile-response"]');
                        if (responseElement) {
                            responseElement.value = solution;
                            element.setAttribute('data-solved', 'true');

                            responseElement.dispatchEvent(new Event('input', { bubbles: true }));
                            responseElement.dispatchEvent(new Event('change', { bubbles: true }));

                            this.solvedCount++;
                            this.log('Turnstile solved successfully!', 'success');
                            this.autoSubmitForm();
                        }
                    }
                }
            } catch (error) {
                this.failedCount++;
                this.log(`Turnstile solve failed: ${error.message}`, 'error');
            } finally {
                this.currentTask = null;
                this.updateGUI();
            }
        }

        async submitCaptcha(params) {
            return new Promise((resolve, reject) => {
                const formData = new FormData();
                formData.append('key', CONFIG.API_KEY);
                formData.append('json', '1');

                Object.keys(params).forEach(key => {
                    formData.append(key, params[key]);
                });

                GM_xmlhttpRequest({
                    method: 'POST',
                    url: `${CONFIG.API_BASE_URL}/in.php`,
                    data: formData,
                    onload: (response) => {
                        try {
                            const result = JSON.parse(response.responseText);
                            if (result.status === 1) {
                                this.log(`Task submitted: ${result.request}`, 'info');
                                resolve(result.request);
                            } else {
                                this.log(`Submit failed: ${result.error_text}`, 'error');
                                reject(new Error(result.error_text));
                            }
                        } catch (error) {
                            this.log(`Parse error: ${error.message}`, 'error');
                            reject(error);
                        }
                    },
                    onerror: (error) => {
                        this.log(`Network error: ${error.message}`, 'error');
                        reject(error);
                    }
                });
            });
        }

        async pollForSolution(taskId) {
            let attempts = 0;
            const maxAttempts = CONFIG.MAX_RETRIES;

            return new Promise((resolve, reject) => {
                const poll = () => {
                    attempts++;

                    if (attempts > maxAttempts) {
                        this.log('Max polling attempts reached', 'error');
                        reject(new Error('Timeout'));
                        return;
                    }

                    GM_xmlhttpRequest({
                        method: 'GET',
                        url: `${CONFIG.API_BASE_URL}/res.php?key=${CONFIG.API_KEY}&action=get&id=${taskId}&json=1`,
                        onload: (response) => {
                            try {
                                const result = JSON.parse(response.responseText);

                                if (result.status === 1) {
                                    this.log(`Solution received after ${attempts} attempts`, 'success');
                                    resolve(result.request);
                                } else if (result.error_text === 'CAPCHA_NOT_READY') {
                                    this.log(`Waiting for solution... (${attempts}/${maxAttempts})`, 'info');
                                    this.updateProgress((attempts / maxAttempts) * 100);
                                    setTimeout(poll, CONFIG.POLLING_INTERVAL);
                                } else {
                                    this.log(`Polling error: ${result.error_text}`, 'error');
                                    reject(new Error(result.error_text));
                                }
                            } catch (error) {
                                this.log(`Parse error: ${error.message}`, 'error');
                                reject(error);
                            }
                        },
                        onerror: (error) => {
                            this.log(`Network error: ${error.message}`, 'error');
                            reject(error);
                        }
                    });
                };

                // Start polling after initial delay
                setTimeout(poll, CONFIG.POLLING_INTERVAL);
            });
        }

        updateProgress(percentage) {
            const progressBar = this.gui.querySelector('#ema-progress');
            if (progressBar) {
                progressBar.style.width = `${percentage}%`;
            }
        }

        autoSubmitForm() {
            // Look for submit buttons and forms
            const submitBtn = document.querySelector('input[type="submit"], button[type="submit"], .submit-btn, #submit');
            const form = document.querySelector('form');

            if (submitBtn && !submitBtn.disabled) {
                this.log('Auto-submitting form...', 'info');
                setTimeout(() => {
                    submitBtn.click();
                }, 1000);
            } else if (form) {
                this.log('Auto-submitting form...', 'info');
                setTimeout(() => {
                    form.submit();
                }, 1000);
            }
        }
    }

    // Initialize the captcha solver
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            new CaptchaSolver();
        });
    } else {
        new CaptchaSolver();
    }

})();
